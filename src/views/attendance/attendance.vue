<template>
  <div class="flex-col flex-1 page">
    <public-header
      :pageName="$t('title.attendance')"
      headerColor="#fff"
    />
    <div class="flex-col justify-start">
      <div class="flex-col group_2">
        <div class="flex-row justify-between section_3">
          <span class="font_3 text_4">{{ formattedDate }}</span>
          <span class="font_3 text_5" @click="openAttendanceRulesDialog">{{ $t('attendance.attendanceRules') }}</span>
        </div>
        <div class="flex-col relative section_4 mt-24">
          <div class="flex-col">
            <div class="flex-row items-center self-stretch group_3">
              <img
                class="image_6"
                src="@/assets/images/entryProcess/fcc7949fcab616c5a26cdd7b8637a518.png"
              />
              <div class="flex-col items-start ml-25 flex-1">
                <span class="font_2 text_7">{{ userName }}</span>
                <span class="font_4">{{$t('attendance.saId')}}: {{ uid }}</span>
              </div>
            </div>
            <span v-if="attendanceRules.attendanceType && attendanceRules.attendanceName" class="self-start font_3 text_9 mt-27">{{ attendanceRules.attendanceType === 1 ? $t('attendance.fixedShifts') : $t('attendance.flexibleShifts') }}: {{ attendanceRules.attendanceName }}</span>
          </div>
          <div class="flex-col group_4">
            <div class="flex-col section_6" :class="{ 'success': clockInStatus === 'normal' }">
              <!-- 状态角标 -->
              <div v-if="shouldShowClockInBadge" class="flex-col justify-start text-wrapper" :class="clockInStatus === 'normal' ? 'normal-badge' : 'abnormal-badge'">
                <span class="text_10">{{ clockInBadgeText }}</span>
              </div>
              <div class="flex-row justify-between items-center sign-time-title">
                <span class="font_5">{{ getWorkStartTimeText() }}</span>
              </div>

              <!-- 已签到状态 -->
              <div class="flex-row items-center mt-19" v-if="isClockIn">
                <img
                  class="image_1 clock-icon"
                  src="../../assets/images/entryProcess/clocked-icon2.png"
                />
                <span class="text_11 ml-13">{{ clockInTime }} {{ $t('attendance.clockedIn') }}</span>
              </div>
              <!-- 未签到状态 -->
              <span v-else class="missing-clock-text">{{ $t('attendance.missingIn') }}</span>
              <!-- 右侧签到按钮 -->
              <div v-if="clockInTipMessage.showClockBtn" class="clock-btn clock-in-btn" @click="handleClockAction('on', clockInTipMessage.isDisabled, clockInTipMessage.message)" :class="{ 'disabled': clockInTipMessage.isDisabled }">
                <span class="text_14" :class="getTextSizeClass($t('attendance.clockIn'))">{{ $t('attendance.clockIn') }}</span>
              </div>
            </div>
            <div class="flex-row items-center group_5" >
              <img
                v-if="clockInTipMessage.showIcon"
                class="image_1 image_9"
                src="../../assets/images/entryProcess/clocked-icon.png"
              />
              <span v-if="clockInTipMessage.message" class="font_1 text_12">{{ clockInTipMessage.message }}</span>
            </div>
            <div class="flex-col section_7 finish-work" :class="{ 'success': clockOutStatus === 'normal' }">
              <!-- 状态角标 -->
              <div v-if="shouldShowClockOutBadge" class="flex-col justify-start text-wrapper badge" :class="clockOutStatus === 'normal' ? 'normal-badge' : 'abnormal-badge'">
                <span class="text_10">{{ clockOutBadgeText }}</span>
              </div>
              <div class="flex-row justify-between items-center sign-time-title">
                <span class="font_5 text_13">{{ getWorkEndTimeText() }}</span>
              </div>

              <!-- 已打卡状态 -->
              <div class="flex-row items-center mt-19" v-if="isClockOut">
                <img
                  class="image_1 clock-icon"
                  src="../../assets/images/entryProcess/clocked-icon2.png"
                />
                <span class="text_11 ml-13">{{ clockOutTime }} {{ $t('attendance.clockedOut') }}</span>
              </div>

              <!-- 未打卡状态 -->
              <span v-else class="missing-clock-text">{{ $t('attendance.missingIn') }}</span>

              <!-- 只有下班打卡后才显示更新链接 -->
              <div v-if="isClockOut && isInRange && !clockOutTipMessage.isDisabled" class="update-clock-link" @click="handleClockAction('off')">
                <span>{{ $t('attendance.updateClockOut') }} ></span>
              </div>

              <!-- 右侧打卡按钮 -->
              <div v-if="clockOutTipMessage.showClockBtn" class="clock-btn clock-out-btn" @click="handleClockAction('off', clockOutTipMessage.isDisabled, clockOutTipMessage.message)" :class="{ 'disabled': clockOutTipMessage.isDisabled }">
                <span class="text_14" :class="getTextSizeClass($t('attendance.clockOut'))">{{ $t('attendance.clockOut') }}</span>
              </div>
            </div>

            <!-- 签退状态提示文字 -->
            <div class="flex-row justify-center items-center clock-tips-container">
              <img
                v-if="clockOutTipMessage.showIcon"
                class="image_1 image_9"
                src="../../assets/images/entryProcess/clocked-icon.png"
              />
              <span v-if="clockOutTipMessage.message" class="font_1 text_12">{{ clockOutTipMessage.message }}</span>
            </div>
          </div>
          <!-- 工作日状态显示 -->
          <div v-if="attendanceRules.workFlag === 1" class="flex-row justify-center items-center section_5 pos_2" :class="{ 'workday-badge': attendanceRules.workFlag === 1 }">
            <img
              class="image_5"
              src="../../assets/images/entryProcess/ic_attendance_working.png"
            />
            <span class="font_1 text_6 ml-12">{{ $t('attendance.workingDay') }}</span>
          </div>
          <!-- 假期状态显示 -->
          <div v-else class="flex-row justify-center items-center section_5 pos_2">
            <img
              class="image_5"
              src="../../assets/images/entryProcess/coffee-icon.png"
            />
            <span class="font_1 text_6 ml-12">{{ $t('attendance.holidays') }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 考勤规则弹窗 -->
    <attendance-rules-dialog
      v-model="showAttendanceRulesDialog"
      :attendance-rules="attendanceRules"
    />
  </div>
</template>

<script>
import { compareMacAddresses, dateFormatTime, calculateGpsDistance } from "@/utils/tools";
import { signIn, reportLog } from "@/axios/api";
import { getGpsInfo, getWifiInfo, checkPermissions, requestPermissions } from "@/api/native";
import { mapState, mapActions, mapMutations } from "vuex";
import { PERMISSION_ENUMS } from "@/enums";
import dayjs, { formatDate, toLocalTime, convertAttendanceTime } from "@/utils/dayjs";
import { timeToMinutes, getTimeDifferenceInMinutes, addMinutesToTime, compareTime } from "@/utils/attendanceUtils";
import AttendanceRulesDialog from "@/components/attendanceRulesDialog.vue";
import { MOCK_SCENARIOS, applyMockData } from '@/utils/attendanceMockData';

export default {
  components: {
    AttendanceRulesDialog
  },
  props: {},
  data() {
    return {
      currentDate: dayjs().format("YYYY-MM-DD"), // 当前日期
      currentTime: "", // 用于存储格式化后的时间
      timer: null, // 定时器
      wifiInfo: {},
      gpsInfo: {},
      showAttendanceRulesDialog: false,
      requestGpsPermissionDone: false, // 是否已经请求过GPS权限
      needsAttendanceRulesUpdate: false, // 是否需要更新考勤规则（日期变化时）
      deviceDataLoaded: false, // 设备数据是否加载完成
      hasReportedDiagnostics: false, // 是否已经上报过诊断信息
      dataLoadingTimeout: null, // 数据加载超时定时器
      maxWaitTime: 5000, // 最大等待时间（5秒）
      // Mock数据控制 - 手动修改这些值来测试不同场景
      mockEnabled: false, // 设置为 true 启用mock数据
      mockScenario: MOCK_SCENARIOS.OUT_RANGE, // 修改这个值来切换场景
    };
  },
  computed: {
    ...mapState(['hrId', 'userInfo', 'deviceId', 'deviceType', 'attendanceRules', 'attendanceRulesLoaded', 'currentSignInfo', 'currentSignInfoLoaded']),
    userName() {
      const { firstName, middleName, lastName } = this.userInfo || {};
      if (!middleName) {
        return `${firstName} ${lastName}`;
      }
      return `${firstName} ${middleName} ${lastName}`;
    },
    isInRange() {
      // 基于新的考勤规则接口数据结构
      const { clockType, clockLocationList } = this.attendanceRules;

      // 如果考勤类型为不限制（0），直接返回 true
      if (clockType === 0) {
        return true;
      }

      // 如果考勤类型为限制（1）但没有位置配置，返回 true
      if (clockType === 1) {
        if (!Array.isArray(clockLocationList) || clockLocationList.length === 0) {
          return false;
        }

        // 遍历所有考勤位置，只要有一个位置验证通过即可
        return clockLocationList.some(location => {
          let gpsValid = true;
          let wifiValid = true;

          // GPS验证：当 gpsFlag === 1 时进行GPS验证
          if (location.gpsFlag === 1) {
            gpsValid = false; // 默认GPS验证失败
            if (Array.isArray(location.gpsList) && location.gpsList.length > 0) {
              // 检查当前GPS位置是否在允许的GPS列表中
              gpsValid = location.gpsList.some(gps => {
                if (!gps.latitude || !gps.longitude) {
                  console.warn('GPS配置坐标无效:', gps);
                  return false;
                }

                // 检查当前用户GPS信息是否可用
                if (!this.gpsInfo?.latitude || !this.gpsInfo?.longitude) {
                  console.warn('用户GPS信息不可用:', this.gpsInfo);
                  return false;
                }

                // 计算当前位置与允许位置之间的距离
                const distanceInMeters = calculateGpsDistance(
                  this.gpsInfo.latitude,
                  this.gpsInfo.longitude,
                  gps.latitude,
                  gps.longitude
                );

                const allowedRange = gps.range; // 单位：米

                const isWithinRange = distanceInMeters <= allowedRange;

                console.log(`GPS验证结果: 距离=${distanceInMeters.toFixed(2)}米, 允许范围=${allowedRange}米, 验证${isWithinRange ? '通过' : '失败'}`);

                return isWithinRange;
              });
            }
          }

          // WIFI验证：当 wifiFlag === 1 时进行WIFI验证
          if (location.wifiFlag === 1) {
            wifiValid = false; // 默认WIFI验证失败
            if (Array.isArray(location.wifiList) && location.wifiList.length > 0) {
              // 检查当前WIFI是否在允许的WIFI列表中
              wifiValid = location.wifiList.some(wifi => compareMacAddresses(wifi.wifiMac, this.wifiInfo?.bssid));
            }
          }
          console.log('🎉 ~ file: attendance.vue ~ line: 236 ~ gpsValid: ', gpsValid);
          console.log('🎉 ~ file: attendance.vue ~ line: 237 ~ wifiValid: ', wifiValid);

          // 如果同时设置了 GPS 和 WIFI,只要满足其中一个即可
          if (location.gpsFlag === 1 && location.wifiFlag === 1) {
            return gpsValid || wifiValid;
          } else if (location.gpsFlag === 1) {
            return gpsValid;
          } else if (location.wifiFlag === 1) {
            return wifiValid;
          } else {
            // 如果该位置既不支持GPS也不支持WIFI，则认为验证通过
            return true;
          }
        });
      }

      return false;
    },
    isClockIn() {
      return this.currentSignInfo?.clockInTime;
    },
    isClockOut() {
      return this.currentSignInfo?.clockOutTime;
    },
    clockInTipMessage() {
      let showIcon = false;
      let message = '';
      let showClockBtn = false;
      let isDisabled = false;

      if (this.isClockIn) {
        // 已签到，显示签到成功
        message = this.$t('attendance.clockInSuccessfully');
        showIcon = true;
      } else {
        // 未签到的情况，检查是否可以打卡
        if (this.isAttendanceGroupConfigured && this.isInRange) {
          message = this.$t('attendance.isInRange');
          showClockBtn = true;
        } else {
          // 有错误情况，显示按钮但禁用
          showClockBtn = true;
          isDisabled = true;
          message = this.errorMessage;
        }
      }
      return {
        message,
        showIcon,
        showClockBtn,
        isDisabled
      };
    },

    clockOutTipMessage() {
      let showIcon = false;
      let message = '';
      let showClockBtn = false;
      let isDisabled = false;

      if (this.isClockOut) {
        message = this.$t('attendance.clockOutSuccessfully');
        showIcon = true;
      } else {
        // 未签退的情况，检查是否可以打卡
        if (this.canClockOut && this.isAttendanceGroupConfigured && this.isInRange) {
          message = this.$t('attendance.isInRange');
          showClockBtn = true;
        } else {
          // 有错误情况或不满足下班打卡条件，显示按钮但禁用
          showClockBtn = true;
          isDisabled = true;

          // 特殊处理：时间窗口内未打上班卡的情况
          if (!this.isClockIn && this.isBeforeClockTimeWindow) {
            message = this.$t('attendance.pleaseClockInFirst');
          } else {
            message = this.errorMessage;
          }
        }
      }

      return {
        message,
        showIcon,
        showClockBtn,
        isDisabled
      };
    },
    clockInTime() {
      const clockInTime = this.attendanceRules?.clockInTime || this.currentSignInfo?.clockInTime;
      if (clockInTime) {
        return toLocalTime(clockInTime, 'HH:mm:ss');
      } else {
        return '';
      }
    },
    clockOutTime() {
      const clockOutTime = this.attendanceRules?.clockOutTime || this.currentSignInfo?.clockOutTime;
      if (clockOutTime) {
        return toLocalTime(clockOutTime, 'HH:mm:ss');
      } else {
        return '';
      }
    },
    formattedDate() {
      const attendanceDate = this.attendanceRules?.attendanceDate;
      // 如果有考勤规则中的日期，使用该日期；否则使用当前日期
      return attendanceDate ? formatDate(attendanceDate, 'YYYY-MM-DD') : this.currentDate;
    },

    // 签到状态 - 直接使用 currentSignInfo 返回的状态值
    clockInStatus() {
      const onStatus = this.currentSignInfo?.onStatus;
      if (onStatus === 0) return 'normal';
      if (onStatus === 1) return 'late';
      if (onStatus === 3) return 'no-clock-in';
      return '';
    },

    // 签退状态 - 直接使用 currentSignInfo 返回的状态值
    clockOutStatus() {
      const offStatus = this.currentSignInfo?.offStatus;
      if (offStatus === 0) return 'normal';
      if (offStatus === 2) return 'early';
      return '';
    },

    // 签到角标文字
    clockInBadgeText() {
      const onStatus = this.currentSignInfo?.onStatus;
      switch (onStatus) {
        case 0:
          return this.$t('attendance.normal');
        case 1:
          return this.$t('attendance.abnormalLateClockIn');
        case 3:
          return this.$t('attendance.abnormalNoClockIn');
        default:
          return this.$t('attendance.normal');
      }
    },

    // 签退角标文字
    clockOutBadgeText() {
      const offStatus = this.currentSignInfo?.offStatus;
      switch (offStatus) {
        case 0:
          return this.$t('attendance.normal');
        case 2:
          return this.$t('attendance.abnormalEarlyClockOut');
        default:
          return this.$t('attendance.normal');
      }
    },

    // 是否显示签到角标 - 基于 currentSignInfo 返回的状态值
    shouldShowClockInBadge() {
      const { onStatus, clockInTime } = this.currentSignInfo;
      if (onStatus === 0) {
        if (clockInTime) {
          return true;
        } else {
          return false
        }
      } else {
        return onStatus !== null && onStatus !== 0;
      }
    },

    // 是否显示签退角标 - 基于 currentSignInfo 返回的状态值
    shouldShowClockOutBadge() {
      const clockOutTime = this.currentSignInfo?.clockOutTime;
      const offStatus = this.currentSignInfo?.offStatus;
      if (!clockOutTime) return false;
      return offStatus !== null && offStatus !== 0;
    },

    // 检查是否有GPS权限
    hasGpsPermission() {
      return !!(this.gpsInfo?.latitude && this.gpsInfo?.longitude);
    },

    // 检查考勤组是否配置
    isAttendanceGroupConfigured() {
      return !!(this.attendanceRules.attendanceName && this.attendanceRules.attendanceName.trim() !== '');
    },

    // 获取当前时间（HH:mm:ss格式）
    getCurrentTime() {
      return dayjs().format('HH:mm:ss');
    },

    // 检查是否在不允许打下班卡的时间范围内
    isBeforeClockTimeWindow() {
      const currentTime = this.getCurrentTime;

      // 获取打卡开始时间
      let startTime = '';
      if (this.attendanceRules.attendanceType === 1) {
        // 固定考勤：使用 workStartTime
        startTime = convertAttendanceTime(
          this.attendanceRules.workStartTimeUTC,
          this.attendanceRules.workStartTime
        );
      } else if (this.attendanceRules.attendanceType === 2) {
        // 弹性考勤：使用 latestClockIn
        startTime = convertAttendanceTime(
          this.attendanceRules.latestClockInUTC,
          this.attendanceRules.latestClockIn
        );
      }

      if (!startTime) {
        return false;
      }

      // 计算时间窗口结束时间（开始时间 + 1小时）
      const windowEndTime = addMinutesToTime(startTime, 60);

      // 检查当前时间是否在不允许打下班卡的时间范围内
      // 从上班时间开始到时间窗口结束（开始时间 + 1小时）都不允许打下班卡
      return compareTime(currentTime, windowEndTime) <= 0;
    },

    // 检查下班打卡是否可用
    canClockOut() {
      // 如果已经下班打卡，不需要再次点击下班打卡按钮
      if (this.isClockOut) {
        return false;
      }

      // 如果已打上班卡
      if (this.isClockIn) {
        // 打了上班卡之后立即可以打下班卡
        return true;
      }

      // 未打上班卡的情况：检查是否在时间窗口内
      if (this.isBeforeClockTimeWindow) {
        // 在时间窗口内且未打上班卡：不允许打下班卡
        return false;
      } else {
        // 超过时间窗口且未打上班卡：允许打下班卡
        return true;
      }
    },

    // 获取错误提示信息
    errorMessage() {
      // 优先级1: 检查考勤组配置
      if (!this.isAttendanceGroupConfigured) {
        return this.$t('attendance.attendanceGroupNotConfigured');
      }

      // 优先级2: 检查GPS权限（仅当需要GPS验证时）
      const needsGpsValidation = this.attendanceRules.clockType === 1 &&
        this.attendanceRules.clockLocationList?.some(location => location.gpsFlag === 1);

      if (needsGpsValidation && this.requestGpsPermissionDone && !this.hasGpsPermission) {
        return this.$t('toastMsg.pleaseEnableGpsPermission');
      }

      // 优先级3: 检查打卡范围
      if (!this.isInRange) {
        return this.$t('attendance.isNotInRange');
      }

      return '';
    },
  },
  watch: {
    // 监听考勤规则加载状态
    attendanceRulesLoaded(newVal) {
      if (newVal) {
        console.log('考勤规则加载完成');
        this.checkDataLoadingComplete();
      }
    },

    // 监听当前签到信息加载状态
    currentSignInfoLoaded(newVal) {
      if (newVal) {
        console.log('当前签到信息加载完成');
        this.checkDataLoadingComplete();
      }
    }
  },
  methods: {
    ...mapActions(['fetchAttendanceRules', 'fetchCurrentSignInfo']),
    ...mapMutations(['SET_ATTENDANCE_RULES', 'SET_CURRENT_SIGN_INFO']),
    // 更新当前时间的方法
    updateCurrentTime() {
      const now = new Date();
      this.currentTime = dateFormatTime(now);

      // 检测日期变化
      const newDate = dayjs().format("YYYY-MM-DD");
      if (newDate !== this.currentDate) {
        console.log(`日期变化检测: ${this.currentDate} -> ${newDate}`);
        this.currentDate = newDate;
        this.needsAttendanceRulesUpdate = true;
      }
    },

    // 获取上班时间显示文本
    getWorkStartTimeText() {
      if (this.attendanceRules.attendanceType === 1) {
        // 固定考勤 - 优先使用UTC时间字段，如果不存在则使用原字段
        const localTime = convertAttendanceTime(
          this.attendanceRules.workStartTimeUTC,
          this.attendanceRules.workStartTime
        );
        if (localTime) {
          const displayTime = this.formatDisplayTime(localTime);
          return this.$t('attendance.startWorkTime', { time: displayTime });
        }
        return this.$t('attendance.startWorkTime', { time: '' });
      } else if (this.attendanceRules.attendanceType === 2) {
        // 弹性考勤 - 优先使用UTC时间字段，如果不存在则使用原字段
        const earliestLocalTime = convertAttendanceTime(
          this.attendanceRules.earliestClockInUTC,
          this.attendanceRules.earliestClockIn
        );
        const latestLocalTime = convertAttendanceTime(
          this.attendanceRules.latestClockInUTC,
          this.attendanceRules.latestClockIn
        );

        const earliestTimeDisplay = this.formatDisplayTime(earliestLocalTime);
        const latestTimeDisplay = this.formatDisplayTime(latestLocalTime);
        return this.$t('attendance.flexibleStart', { timeRange: `${earliestTimeDisplay}-${latestTimeDisplay}` });
      }
      return this.$t('attendance.startWorkTime', { time: '' });
    },
    // 获取下班时间显示文本
    getWorkEndTimeText() {
      if (this.attendanceRules.attendanceType === 1) {
        // 固定考勤 - 优先使用UTC时间字段，如果不存在则使用原字段
        const localTime = convertAttendanceTime(
          this.attendanceRules.workEndTimeUTC,
          this.attendanceRules.workEndTime
        );
        if (localTime) {
          const displayTime = this.formatDisplayTime(localTime);
          return this.$t('attendance.finishWorkTime', { time: displayTime });
        }
        return this.$t('attendance.finishWorkTime', { time: '' });
      } else if (this.attendanceRules.attendanceType === 2) {
        // 弹性考勤 - 计算最早下班时间：最早上班时间 + 工作时长 + 休息时间
        const earliestFinishTime = this.calculateEarliestFinishTime();
        if (earliestFinishTime) {
          const displayTime = this.formatDisplayTime(earliestFinishTime);
          return this.$t('attendance.flexibleFinish', { time: displayTime });
        }
        return this.$t('attendance.flexibleFinish', { time: '' });
      }
      return this.$t('attendance.finishWorkTime', { time: '' });
    },
    // 计算弹性考勤的最早下班时间
    calculateEarliestFinishTime() {
      // 获取最早上班时间
      const earliestClockInTime = convertAttendanceTime(
        this.attendanceRules.earliestClockInUTC,
        this.attendanceRules.earliestClockIn
      );

      if (!earliestClockInTime) {
        return '';
      }

      // 获取工作时长（分钟）- workDuration 是时间格式 "08:00:00"
      const workDuration = this.attendanceRules.workDuration;
      const workMinutes = timeToMinutes(workDuration);

      // 计算休息时间（分钟）
      let breakMinutes = 0;
      if (this.attendanceRules.breakFlag === 1) {
        const breakStartTime = convertAttendanceTime(
          this.attendanceRules.breakStartTimeUTC,
          this.attendanceRules.breakStartTime
        );
        const breakEndTime = convertAttendanceTime(
          this.attendanceRules.breakEndTimeUTC,
          this.attendanceRules.breakEndTime
        );

        if (breakStartTime && breakEndTime) {
          breakMinutes = getTimeDifferenceInMinutes(breakStartTime, breakEndTime);
        }
      }

      // 计算最早下班时间：最早上班时间 + 工作时长 + 休息时间
      const totalMinutesToAdd = workMinutes + breakMinutes;
      return addMinutesToTime(earliestClockInTime, totalMinutesToAdd);
    },
    // 格式化显示时间，处理跨日情况
    formatDisplayTime(timeStr) {
      if (!timeStr || timeStr === '') return timeStr || '';

      // 处理 HH:mm:ss 格式，只显示 HH:mm
      const timeParts = timeStr.split(':');
      if (timeParts.length >= 2) {
        let hours = parseInt(timeParts[0]);
        const minutes = parseInt(timeParts[1]);

        // 处理时区转换后时间大于24的情况，显示减去24的时间
        if (hours >= 24) {
          hours = hours - 24;
        }

        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
      }

      return timeStr;
    },
    async initSignInfo() {
      await Promise.all([
        this.checkAttendanceGroupConfiguration(),
        this.fetchCurrentSignInfo()
      ]);
      // 如果启用了Mock数据，应用Mock场景
      if (this.mockEnabled && this.mockScenario) {
        applyMockData(this, this.mockScenario, this.currentDate);
      }
    },
    async initDeviceData() {
      try {
        const [wifiInfo, gpsInfo] = await Promise.all([getWifiInfo(), getGpsInfo()]);
        this.wifiInfo = wifiInfo;
        this.gpsInfo = gpsInfo;
        this.deviceDataLoaded = true;
        console.log('设备数据加载完成');

        // 检查是否可以上报诊断信息
        this.checkDataLoadingComplete();
      } catch (err) {
        console.log('🎉 ~ file: attendance.vue ~ line: 578 ~ err: ', err);
        this.deviceDataLoaded = true; // 即使失败也标记为完成，避免阻塞
        this.checkDataLoadingComplete();
      }
    },

    // 检测考勤组配置
    async checkAttendanceGroupConfiguration() {
      try {
        this.fetchAttendanceRules();
      } catch (error) {
        console.error('检测考勤组配置失败:', error);
      }
    },

    // 检查并更新考勤规则（当日期变化时）
    async checkAndUpdateAttendanceRules() {
      if (this.needsAttendanceRulesUpdate) {
        console.log('主动更新考勤规则，因为检测到日期变化');
        try {
          await this.fetchAttendanceRules();
          this.needsAttendanceRulesUpdate = false;
        } catch (error) {
          console.error('更新考勤规则失败:', error);
        }
      }
    },

    async handleClockAction(signType, disabled = false, message) {
      // 检查设备ID
      if (!this.deviceId) {
        this.$toast(this.$t('attendance.deviceIdNotAvailable'));
        return;
      }
      if (disabled) {
        if (message) this.$toast(message);
        return;
      }
      try {
        const data = {
          signType,
          // 枚举值：Wi-Fi mobile Unknown
          networkType: this.wifiInfo?.networkType || 'Unknown',
          latitude: this.gpsInfo?.latitude || '0',
          longitude: this.gpsInfo?.longitude || '0',
          wifiMacAddr: this.wifiInfo?.bssid || '',
          networkName: this.wifiInfo?.ssid || '',
          deviceId: this.deviceId,
          deviceType: this.deviceType,
        }
        await signIn(data);
        await this.fetchCurrentSignInfo();

        // 检查是否需要更新考勤规则（日期变化后的首次打卡）
        this.checkAndUpdateAttendanceRules();

        setTimeout(() => {
          if (signType === 'on') {
            this.$toast(this.$t('attendance.clockInSuccessfully'))
          } else {
            this.$toast(this.$t('attendance.clockOutSuccessfully'))
          }
        }, 0);
      } catch (err) {
        console.log('🎉 ~ file: attendance.vue ~ line: 635 ~ err: ', err);
      }
    },

    // 打开考勤规则弹窗
    openAttendanceRulesDialog() {
      this.showAttendanceRulesDialog = true;
    },

    // 根据文字长度返回相应的CSS类名
    getTextSizeClass(text) {
      if (!text) return '';

      const textLength = text.length;

      // 根据文字长度返回不同的CSS类
      if (textLength <= 10) {
        return ''; // 使用默认字体大小
      } else {
        return 'long-text';
      }
    },

    // 检查所有数据是否加载完成
    checkDataLoadingComplete() {
      const isComplete = this.attendanceRulesLoaded &&
                        this.currentSignInfoLoaded &&
                        this.deviceDataLoaded &&
                        this.requestGpsPermissionDone;

      // 如果数据加载完成且还未上报过诊断信息，则进行上报
      if (isComplete && !this.hasReportedDiagnostics) {
        console.log('所有数据加载完成，开始上报诊断信息');
        this.clearDataLoadingTimeout();
        this.reportAttendanceDiagnostics();
      }

      return isComplete;
    },

    // 启动数据加载超时检查
    startDataLoadingTimeout() {
      if (this.dataLoadingTimeout) {
        clearTimeout(this.dataLoadingTimeout);
      }

      this.dataLoadingTimeout = setTimeout(() => {
        if (!this.hasReportedDiagnostics) {
          console.log('数据加载超时，强制上报诊断信息');
          this.reportAttendanceDiagnostics();
        }
      }, this.maxWaitTime);
    },

    // 清除数据加载超时定时器
    clearDataLoadingTimeout() {
      if (this.dataLoadingTimeout) {
        clearTimeout(this.dataLoadingTimeout);
        this.dataLoadingTimeout = null;
      }
    },

    // 上报考勤诊断信息
    async reportAttendanceDiagnostics() {
      try {
        // 检查GPS权限验证需求
        const needsGpsValidation = this.attendanceRules.clockType === 1 &&
          this.attendanceRules.clockLocationList?.some(location => location.gpsFlag === 1);
        // 检查WIFI权限验证需求
        const needsWifiValidation = this.attendanceRules.clockType === 1 &&
          this.attendanceRules.clockLocationList?.some(location => location.wifiFlag === 1);

        // 构造详细的诊断日志内容
        const diagnosticsContent = {
          pageLoadTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          attendanceDate: this.formattedDate,
          currentTime: this.getCurrentTime,
          reportTrigger: this.dataLoadingTimeout ? 'timeout' : 'complete', // 上报触发原因

          // 数据加载状态
          loadingStatus: {
            attendanceRulesLoaded: this.attendanceRulesLoaded,
            currentSignInfoLoaded: this.currentSignInfoLoaded,
            deviceDataLoaded: this.deviceDataLoaded,
            requestGpsPermissionDone: this.requestGpsPermissionDone
          },

          // 考勤规则信息
          attendanceRules: {
            loaded: this.attendanceRulesLoaded,
            id: this.attendanceRules?.id || '',
            attendanceName: this.attendanceRules?.attendanceName || '',
            clockType: this.attendanceRules?.clockType,
            attendanceType: this.attendanceRules?.attendanceType,
            clockInTime: this.clockInTime,
            clockOutTime: this.clockOutTime,
            clockLocationList: this.attendanceRules?.clockLocationList || []
          },

          // 当前签到状态
          currentSignInfo: {
            loaded: this.currentSignInfoLoaded,
            isClockIn: this.isClockIn,
            isClockOut: this.isClockOut,
            canClockIn: this.canClockIn,
            canClockOut: this.canClockOut
          },

          // 设备和位置信息
          deviceInfo: {
            deviceId: this.deviceId,
            deviceType: this.deviceType,
            hasGpsPermission: this.hasGpsPermission,
            requestGpsPermissionDone: this.requestGpsPermissionDone,
            gpsInfo: this.gpsInfo,
            wifiInfo: this.wifiInfo
          },

          // 打卡可用性诊断
          clockAvailability: {
            isAttendanceGroupConfigured: this.isAttendanceGroupConfigured,
            isInRange: this.isInRange,
            needsGpsValidation,
            needsWifiValidation,
            isBeforeClockTimeWindow: this.isBeforeClockTimeWindow,
            errorMessage: this.errorMessage
          }
        };

        // 构造上报数据
        const reportData = {
          type: "CLOCK",
          networkType: this.wifiInfo?.networkType || 'Unknown',
          latitude: this.gpsInfo?.latitude || '0',
          longitude: this.gpsInfo?.longitude || '0',
          wifiMacAddr: this.wifiInfo?.bssid || '',
          networkName: this.wifiInfo?.ssid || '',
          deviceId: this.deviceId,
          deviceType: this.deviceType,
          content: JSON.stringify(diagnosticsContent)
        };

        console.log('上报考勤诊断信息:', reportData);
        await reportLog(reportData);
        console.log('考勤诊断信息上报成功');
        this.hasReportedDiagnostics = true;
        this.clearDataLoadingTimeout();
      } catch (error) {
        console.error('考勤诊断信息上报失败:', error);
        // 不影响正常页面功能，只记录错误日志
      }
    },
  },
  async created() {
    // 启动数据加载超时检查
    this.startDataLoadingTimeout();

    // 初始化考勤信息（获取签到信息）
    this.initSignInfo();
    // 先检查GPS权限状态，再根据结果决定是否请求权限
    try {
      const permissionResult = await checkPermissions({
        permissions: [PERMISSION_ENUMS.ACCESS_COARSE_LOCATION]
      });

      // 检查deny数组：如果为空数组，说明权限已获得
      if (permissionResult.deny === '[]' || (Array.isArray(permissionResult.deny) && permissionResult.deny.length === 0)) {
        // 权限已获得，直接初始化设备数据
        this.initDeviceData();
        this.requestGpsPermissionDone = true;
        this.checkDataLoadingComplete();
      } else {
        // 权限未获得，请求GPS权限
        // eslint-disable-next-line promise/catch-or-return
        requestPermissions({
          permissions: [PERMISSION_ENUMS.ACCESS_COARSE_LOCATION]
        }).then(() => {
          this.initDeviceData();
        }).catch(() => {
          this.$toast(this.$t('toastMsg.pleaseEnableGpsPermission'));
        }).finally(() => {
          this.requestGpsPermissionDone = true; // 标记GPS权限请求已完成
          this.checkDataLoadingComplete();
        });
      }
    } catch (error) {
      console.log('checkPermissions failed', error);
    }
  },
  mounted() {
    // 初始化时间并开启定时器
    this.updateCurrentTime();
    this.timer = setInterval(() => {
      this.updateCurrentTime();
    }, 1000); // 每秒更新一次
  },
  beforeDestroy() {
    // 清除定时器，防止内存泄漏
    if (this.timer) {
      clearInterval(this.timer);
    }
    // 清除数据加载超时定时器
    this.clearDataLoadingTimeout();
  },
};
</script>

<style scoped lang="scss">
.ml-25 {
  margin-left: 25px;
}
.mt-27 {
  margin-top: 27px;
}
.mt-19 {
  margin-top: 19px;
}
.ml-13 {
  margin-left: 13px;
}
.success-notification {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
  margin-top: 10px;
}

.success-check-icon {
  width: 20px;
  height: 20px;
  background-color: #34C759;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.success-message-text {
  color: #34C759;
  font-size: 14px;
  font-weight: 500;
}

.missing-clock-text {
  color: #999999;
  font-size: 28px;
  margin-top: 15px;
  display: block;
}

.normal-badge {
  background-color: #13a869 !important;
}

.abnormal-badge {
  background-color: #ff4242 !important;
}

.no-clock-required {
  background-color: #13a869 !important;
}

.clock-tips-container {
  margin-top: 46px;
  margin-bottom: 10px;
  padding: 0 20px;
}

.clock-tips-text {
  color: #7b8da8;
  font-size: 24px;
  line-height: 23px;
  text-align: center;
  font-weight: 400;
}

.page {
  background-color: #f1f3f6;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  .section_2 {
    padding: 45px 32px 46.5px;
    background-color: #ffffff;
    border-bottom: solid 1px #d5e1ec;
    .image_4 {
      width: 44px;
      height: 44px;
    }
    .pos {
      position: absolute;
      left: 32px;
      top: 50%;
      transform: translateY(-50%);
    }
    .text_3 {
      line-height: 27.5px;
    }
  }
  .group_2 {
    padding: 21.5px 24px 40px;
    .section_3 {
      padding: 19px 22px 19.5px 25.5px;
      background-color: #ffffff;
      border-radius: 16px;
      .text_4 {
        line-height: 20.5px;
      }
      .text_5 {
        color: #0074ff;
        cursor: pointer;
        transition: opacity 0.2s;

        &:hover {
          opacity: 0.8;
        }

        &:active {
          opacity: 0.6;
        }
      }
    }
    .section_4 {
      padding: 27px 21px 136px 24px;
      background-color: #ffffff;
      border-radius: 16px;
      box-shadow: 0 6px 20px #0000001a;
      .group_3 {
        padding-bottom: 30.5px;
        border-bottom: solid 1px #e3e5e9;
        .image_6 {
          width: 120px;
          height: 120px;
        }
        .text_7 {
          max-width: 65%;
          font-weight: 700;
          line-height: 34.5px;
          word-break: break-all;
        }
        .text_8 {
          line-height: 27px;
        }
      }
      .text_9 {
        margin-left: 2.5px;
        line-height: 32px;
      }
      .group_4 {
        margin-top: 66.5px;
        .section_6 {
          margin-right: 3px;
          padding: 26px 24px 73px;
          background-color: #f1f3f6;
          position: relative;
          &.success {
            background-color: #eff6ff;
          }
        }
        .sign-time-title {
          max-width: 270px;
          word-break: break-word;
        }

        .text-wrapper {
          position: absolute;
          right: 0;
          bottom: 100%;
          max-width: 306px;
          padding: 0 12px 2px;
          background-color: #13a869;
          .text_10 {
            color: #ffffff;
            font-size: 24px;
            line-height: 28px;
          }
        }

        .text_11 {
          color: #7b8da8;
          font-size: 24px;
          line-height: 18px;
        }

        // 通用的打卡按钮样式
        .clock-btn {
          cursor: pointer;
          position: absolute;
          right: 20px;
          top: 50%;
          transform: translateY(-50%);
          width: 132px;
          height: 132px;
          filter: drop-shadow(0px 5px 6px #c8d1dc);
          background-image: linear-gradient(180deg, #5db4ff 0%, #0074ff 100%);
          box-shadow: 0px -5px 14px #004ca94d inset, 0px 3.5px 4px #ffffff4d inset;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;

          &.disabled {
            background-image: linear-gradient(180deg, #D2D9E0 0%, #9CA6AF 100%);
            cursor: not-allowed;
            box-shadow: 0 0.06rem 0.22rem rgba(0, 0, 0, 0.1);

            &:hover {
              transform: translateY(-50%);
            }
            .text_14 {
              color: rgba(255, 255, 255, 0.5);
            }
          }

          .text_14 {
            color: white;
            font-size: 26px;
            font-weight: 500;
            text-align: center;
            position: static;
            transform: none;
            width: 122px; // 限制文字宽度，为圆形按钮留出边距
            max-width: 122px;
            word-wrap: break-word;
            word-break: break-word;
            white-space: normal;
            line-height: 1.2;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            box-sizing: border-box;

            // 根据文字长度动态调整字体大小
            &.long-text {
              font-size: 24px;
            }
          }
        }

        // 签到按钮使用通用样式
        .clock-in-btn {
          @extend .clock-btn;
        }

        // 签退按钮使用通用样式
        .clock-out-btn {
          @extend .clock-btn;
        }
        .group_5 {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 92%;
          height: 115px;
          margin: 15px auto;
          padding: 15.5px 34px 15.5px;
          background-image: url('../../assets/images/entryProcess/line.png');
          background-repeat: no-repeat;
          background-position: 15.5px center;
          background-size: 12px 89px;
        }
        .text_12 {
          margin-left: 9.5px;
          color: #7b8da8;
          line-height: 23.5px;
        }
        .image_1 {
          width: 32px;
          height: 32px;
        }
        .section_7 {
          min-height: 180px;
          margin-right: 3px;
          padding: 24px 26.5px;
          background-color: #f1f3f6;
          position: relative;
          &.success {
            background-color: #eff6ff;
          }
          .update-clock-link {
            margin-top: 10px;
            cursor: pointer;
            margin-bottom: 10px;

            span {
              color: #007AFF;
              font-size: 24px;
              font-weight: 400;
              text-decoration: none;
            }
          }
        }
        .font_5 {
          font-size: 28px;
          color: #1b3155;
          word-break: break-word;
          display: block;
          width: 100%;
          line-height: 32px;
        }
      }
      .section_5 {
        padding: 6px 10px 8px;
        background-color: #13a869;
        border-radius: 0 16px 0 16px;
        //width: 200px;
        &.workday-badge {
          background-color: #0074ff;
        }
        .image_5 {
          width: 26px;
          height: 28px;
        }
        .text_6 {
          color: #ffffff;
        }
      }
      .pos_2 {
        position: absolute;
        right: 0;
        top: 0;
      }
      .font_1 {
        font-size: 24px;
        line-height: 23px;
      }
    }
    .font_3 {
      font-size: 28px;
      line-height: 21.5px;
      color: #7b8da8;
    }
  }
  .font_2 {
    font-size: 36px;
    color: #1b3155;
  }
  .font_4 {
    margin-top: 10px;
    font-size: 24px;
    line-height: 23px;
    color: #7b8da8;
  }
}
</style>
