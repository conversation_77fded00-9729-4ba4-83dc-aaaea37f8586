<template>
  <div v-if="userInfo" class="home-container flex-col pt-46">
    <div class="flex-row items-center justify-between group_3">
      <div class="flex-row items-center flex-1">
        <img v-if="!isIOS" class="home-back" src="@/assets/images/common/back.png" v-debounce="handleGoBack"/>
        <img
          class="image_4"
          src="@/assets/images/entryProcess/e254478f2918f87172a104f96eab9aa3.png"
        />
        <span class="text_3 ml-28 flex-1">{{ userName }}</span>
      </div>
      <div class="flex-row items-center">
        <lang-selector />
      </div>
    </div>
    <div class="flex-col mt-24">
      <div class="banner-wrapper">
        <img
          class="image_5"
          src="../../assets/images/entryProcess/banner.png"
        />
        <p class="text">{{ $t('home.bannerText', {appName: 'HR APP'}) }}</p>
      </div>
      <div class="flex-col section_2 mt-24">
        <div>
          <span class="self-start font_2 text_4">{{$t('home.functionMenu')}}</span>
          <div class="flex-row equal-division mt-36">
            <div v-if="isPassed" class="flex-col items-start section_3" @click="gotoAttendance">
              <img
                class="image_7"
                src="../../assets/images/entryProcess/attendence-icon.png"
              />
              <span class="font_1 text_5 mt-29">{{$t('home.attendance')}}</span>
            </div>
            <div v-else class="flex-col section_4" v-debounce="handleGotoProfile">
              <img
                class="self-start image_7"
                src="../../assets/images/entryProcess/profile-icon.png"
              />
              <span class="self-center font_3 text_6 mt-29">{{$t('home.myProfile')}}</span>
            </div>
          </div>
        </div>
        <div v-if="showTodoItems" class=" mt-36">
          <span class="self-start font_2">{{$t('home.todoItems')}}</span>
          <div v-if="isPassed && currentSignInfoLoaded && isNotClockDone" class="flex-col self-stretch mt-36">
            <div class="flex-col section_1">
              <span class="self-start font_3 text_7">{{ isClockIn ? $t('home.clockOut') : $t('home.clockIn') }}</span>
              <div class="flex-row justify-end self-stretch mt-20">
                <div class="flex-col justify-center items-center text-wrapper view" @click="gotoAttendance">
                  <span class="font_5">{{$t('home.toComplete')}}</span>
                </div>
              </div>
            </div>
          </div>
          <div v-if="isRejected || isInviting" class="flex-col self-stretch mt-36">
            <div class="flex-col section_1">
              <span class="self-start font_3 text_7">{{ supplementText }}</span>
              <div class="flex-row justify-end self-stretch mt-20">
                <!--              <div class="flex-col items-start">
                                <span class="font_4">{{$t('home.deadline')}}:</span>
                                <span class="font_4">14:56:30 22-03-2021</span>
                              </div>-->
                <div v-if="isRejectedManual || isInviting" class="flex-col justify-center items-center text-wrapper view" v-debounce="toCompleted">
                  <span class="font_5">{{$t('home.toComplete')}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from "vuex";
import { INVITING_STATUS_ENUMS } from "@/enums";
import { authByCode, appTo } from "@/axios/api";
import { disableNativeBack, enableNativeBack, loginEM, sendCode, finishHtmlPage } from "@/api/native";
import LangSelector from '@/components/lang-selector.vue'
import { isIOS } from "@/utils/tools";

const { SCREENING, PENDING, CHEKING, PASSED_SYSTEM, PASSED_MANUAL, REJECTED_MANUAL, REJECTED_SYSTEM, INVITING } = INVITING_STATUS_ENUMS;
export default {
  name: 'Home',
  components: {
    LangSelector
  },
  data() {
    return {
      fromRouteName: '',
      isIOS: isIOS(),
    };
  },
  computed: {
    ...mapState(['userInfo', 'token', 'attendanceRules', 'attendanceRulesLoaded', 'currentSignInfo', 'currentSignInfoLoaded']),
    isPassed() {
      return [PASSED_SYSTEM, PASSED_MANUAL].includes(this.userInfo?.status);
    },
    isRejected() {
      return [REJECTED_MANUAL, REJECTED_SYSTEM].includes(this.userInfo?.status);
    },
    isRejectedManual() {
      return this.userInfo?.status === REJECTED_MANUAL;
    },
    isRejectedSystem() {
      return this.userInfo?.status === REJECTED_SYSTEM;
    },
    supplementText() {
      if (this.isRejectedSystem) {
        return this.$t('home.rejectedSystem');
      }
      if (this.isRejectedManual) {
        return this.$t('home.supplementModify')
      }
      return this.$t('home.supplement')
    },
    isInviting() {
      return this.userInfo?.status === INVITING;
    },
    isPending() {
      return [SCREENING, PENDING, CHEKING].includes(this.userInfo?.status);
    },
    userName() {
      const { firstName, middleName, lastName } = this.userInfo;
      if (!middleName) {
        return `${firstName} ${lastName}`;
      }
      return `${firstName} ${middleName} ${lastName}`;
    },
    isNotClockDone() {
      // 基于 currentSignInfo 的状态值判断是否需要显示todo
      if (!this.currentSignInfoLoaded) {
        return false;
      }

      const isClockIn = this.isClockIn;
      const isClockOut = this.isClockOut;
      const onStatus = this.currentSignInfo?.onStatus;
      const offStatus = this.currentSignInfo?.offStatus;

      // 根据统一的状态枚举值判断是否显示todo
      // 显示todo的情况：
      // 1. 上班未打卡且需要打卡时
      // 2. 下班未打卡且需要打卡时
      // 3. 下班早退时（offStatus = 2）
      // 4. 忘记打卡时（onStatus = 3）

      // 不显示todo的情况：
      // 1. 正常上班打卡（onStatus = 0）
      // 2. 迟到但已打卡（onStatus = 1）

      if (this.attendanceRules.workFlag === 0) {
        // 如果考勤规则中 workFlag 为 0，表示不需要打卡
        return false;
      }

      // 如果上班未打卡或状态异常，显示todo
      if (!isClockIn || (onStatus !== null && onStatus !== 0)) {
        return true;
      }

      // 如果下班未打卡，显示todo
      if (!isClockOut) {
        return true;
      }

      // 如果下班早退，显示todo
      if (offStatus === 2) {
        return true;
      }

      return false;
    },
    isClockIn() {
      return this.currentSignInfo?.clockInTime;
    },
    isClockOut() {
      return this.currentSignInfo?.clockOutTime;
    },
    showTodoItems() {
      if (this.isPassed && this.currentSignInfoLoaded && this.isNotClockDone) {
        return true;
      }
      if (this.isPending) {
        return false;
      }
      return this.isRejected || this.isInviting
    },


  },
  created() {
    const { code } = this.$route.query;
    if (code) {
      this.authByCode(code);
    } else {
      this.initData();
    }
  },
  mounted() {
    disableNativeBack(() => {
      this.handleGoBack();
    });
  },
  beforeDestroy() {
    enableNativeBack();
  },
  methods: {
    ...mapActions(['fetchInviteInfo', 'setToken', 'removeAllToken', 'fetchAttendanceRules', 'fetchCurrentSignInfo']),
    async authByCode(code) {
      let authSuccess = false;
      try {
        const data = await authByCode({
          code,
        })
        const currentToken = this.token;
        console.log('🎉 ~ file: home.vue ~ line: 179 ~ currentToken: ', currentToken);
        if (data.token) {
          console.log('🎉 ~ file: home.vue ~ line: 181 ~ data.token: ', data.token);
          authSuccess = true;
          this.removeAllToken();
          this.setToken(data.token);
          this.initData();
          const loginEMData = {
            uid: data.hrId,
            token: data.token,
            account: data.email,
          }
          this.$store.commit('SET_HRID', data.hrId);
          if (this.token !== data.token) {
            loginEM(loginEMData);
          }
          this.$router.replace({
            query: {
              ...this.$route.query,
              code: ''
            }
          });
        } else {
          authSuccess = false;
        }
      } catch (err) {
        authSuccess = false;
      }
      if (!authSuccess) {
        this.$router.push('/login-input');
      }
    },
    async initData() {
      await this.fetchInviteInfo();
      const token = localStorage.getItem('token');
      if (this.isPassed) {
        if (token) {
          // 并行获取考勤规则和当前打卡信息
          try {
            await Promise.all([
              this.fetchAttendanceRules(),
              this.fetchCurrentSignInfo()
            ]);
          } catch (err) {
            console.log('获取考勤数据失败:', err);
            // 即使获取失败也不阻止用户使用其他功能
          }
        } else {
          localStorage.removeItem('tempToken');
          this.$router.replace('/login-input');
          this.$toast(this.$t('toastMsg.profileChanged'));
        }
      }
    },

    gotoAttendance() {
      this.$router.push('/attendance');
    },
    gotoPersonInfo() {
      this.$router.push('/personInfo');
    },
    async handleGotoProfile() {
      if (this.isRejected || this.isInviting || this.isPassed) return;
      if (this.isPending) {
        // 此时状态为Pending时，需要重新获取一次数据
        await this.initData()
        if (this.isPending) {
          return this.$toast(this.$t('toastMsg.isPending'));
        }
      }
    },
    toCompleted() {
      if (this.isRejected) {
        this.$router.push('/result');
      } else {
        this.gotoPersonInfo();
      }
    },
    async handleGoBack() {
      const from = this.$route.query.from || localStorage.getItem('from');
      const country = this.$route.query.country || localStorage.getItem('country');
      try {
        // 获取code
        const data = await appTo({
          app: from,
          country,
        });
        console.log('🎉 ~ file: home.vue ~ line: 266 ~ appTo data: ', JSON.stringify(data));
        // 调用sendCode，没有code时传入null
        sendCode({ code: data.code || 'null', from, country });
      } catch (error) {
        console.error('Return failed:', JSON.stringify(error));
        sendCode({ code: 'null', from, country });
      } finally {
        finishHtmlPage();
      }
    }
  }
};
</script>

<style scoped lang="scss">
.home-container {
  background: rgba(239, 243, 246, 0.80);
  backdrop-filter: blur(42.13336944580078px);
}
.pt-46 {
  padding-top: 46px;
}
.ml-29 {
  margin-left: 29px;
}
.mt-29 {
  margin-top: 29px;
}
.mt-25 {
  margin-top: 25px;
}
.group_3 {
  padding: 0 30px;
  .home-back {
    display: inline-block;
    width: 44px;
    height: 44px;
    left: 24px;
    top: 36px;
    margin-right: 20px;
  }
  .image_4 {
    width: 80px;
    height: 80px;
  }
  .text_3 {
    color: #1b3155;
    font-size: 36px;
    font-weight: 700;
    line-height: 1.2;
    word-break: break-word;
    overflow-wrap: break-word;
  }
}
.return-btn {
  font-size: 28px;
  font-weight: 400;
  line-height: 28.13px;
  text-align: left;
  color: #0074FF;
}
.banner-wrapper {
  position: relative;
  .text {
    position: absolute;
    width: 460px;
    height: 96px;
    top: 31px;
    left: 50px;
    font-size: 30px;
    font-style: italic;
    font-weight: 800;
    line-height: 47.6px;
    color: #FFFFFF;
  }
}
.image_5 {
  margin: 0 24px;
  width: 93.3333vw;
  height: 22.5vw;
}
.section_2 {
  padding: 53.5px 36px 146px;
  background-color: #ffffff;
  border-radius: 48px 48px 0 0;
  box-shadow: 0px -3px 16px #3c7af71a;
  .font_2 {
    font-size: 28px;
    line-height: 21.5px;
    font-weight: 700;
    color: #1b3155;
  }
  .text_4 {
    line-height: 21px;
  }
  .equal-division {
    align-self: flex-start;
    .section_3 {
      margin-right: 30px;
      padding: 23.5px 16px 64px;
      border-radius: 16px;
      background: linear-gradient(180deg, #F9FCFE 0%, #F5FAFF 100%);
      width: 196px;
      height: 196px;
    }
    .section_4 {
      padding: 23.5px 16px 59px;
      background-image: linear-gradient(180deg, #fef9fb 0%, #fcf9f9 100%);
      border-radius: 16px;
      width: 196px;
      height: 196px;
    }
    .image_7 {
      width: 61.5px;
      height: 61.5px;
    }
    .text_5 {
      margin-left: 4px;
    }
    .text_6 {
      line-height: 23.5px;
    }
  }
  .section_1 {
    padding: 34px 25.5px 18.5px;
    background-color: #fafafe;
    border-radius: 8px;
    .text_7 {
      line-height: 28px;
    }
    .view {
      margin: 5px 4.5px 5.5px 0;
    }
  }
  .font_3 {
    font-size: 24px;
    line-height: 23px;
    color: #1b3155;
  }
  .section_5 {
    padding: 34px 25.5px 18.5px 25.5px;
    background-color: #fafafe;
    border-radius: 8px;
    .view_2 {
      margin: 5px 4.5px 5.5px 0;
    }
  }
  .font_1 {
    font-size: 24px;
    line-height: 18px;
    color: #1b3155;
  }
  .font_4 {
    font-size: 24px;
    line-height: 40px;
    color: #7b8da8;
  }
  .text-wrapper {
    background-color: #0074ff;
    border-radius: 49px;
    width: 196px;
    height: 64px;
    .font_5 {
      font-size: 24px;
      line-height: 23px;
      color: #ffffff;
    }
  }
}
</style>
