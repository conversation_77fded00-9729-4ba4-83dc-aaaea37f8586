import { fetchInviteInfo, inviteSubmit, inviteUpdate, uploadFile, getAttendanceRule, getSignCurrentSign } from "@/axios/api";

// 添加错误日志格式化函数
const formatError = (error) => {
  if (typeof error === 'string') return error;
  if (error instanceof Error) return error.message;
  try {
    return JSON.stringify(error);
  } catch (e) {
    return error;
  }
};

export default {
  // 获取用户信息
  async fetchInviteInfo({ commit }) {
    try {
      const data = await fetchInviteInfo(); // 获取用户信息的接口
      commit('SET_USER_INFO', data);
      return data;
    } catch (error) {
      console.error('获取用户信息失败:', formatError(error));
      throw error; // 抛出错误，以便调用者可以捕获并处理
    }
  },

  // 更新表单
  async updateForm({ commit, state }, formData) {
    // 防止重复提交
    if (state.isSubmitting) return;
    commit('SET_IS_SUBMITTING', true);
    try {
      // formData的值去前后空格
      Object.keys(formData).forEach(key => {
        if (typeof formData[key] === 'string') {
          formData[key] = formData[key].trim();
        }
      });
      const data = await inviteUpdate({
        id: state.userInfo?.id,
        firstName: state.userInfo?.firstName,
        lastName: state.userInfo?.lastName,
        deviceId: state.deviceId,
        deviceType: state.deviceType,
        ...formData
      }); // 更新表单
      commit('SET_FORM_STATUS', '更新成功');
      commit('SET_FORM_DATA', formData);
      return data;
    } catch (error) {
      console.error('更新表单失败:', formatError(error));
      commit('SET_FORM_STATUS', '更新失败');
      throw error; // 抛出错误，以便调用者可以捕获并处理
    } finally {
      commit('SET_IS_SUBMITTING', false);
    }
  },

  // 提交表单
  async submitForm({ commit, state }, formData) {
    // 防止重复提交
    if (state.isSubmitting) return;
    commit('SET_IS_SUBMITTING', true);
    try {
      // formData的值去前后空格
      Object.keys(formData).forEach(key => {
        if (typeof formData[key] === 'string') {
          formData[key] = formData[key].trim();
        }
      });
      const data = await inviteSubmit({
        deviceId: state.deviceId,
        deviceType: state.deviceType,
        ...formData
      }); // 提交表单
      commit('SET_FORM_STATUS', '提交成功');
      return data;
    } catch (error) {
      console.error('提交表单失败:', formatError(error));
      commit('SET_FORM_STATUS', '提交失败');
      throw error; // 抛出错误，以便调用者可以捕获并处理
    } finally {
      commit('SET_IS_SUBMITTING', false);
    }
  },

  async uploadFile({ commit, state }, file) {
    if (state.isSubmitting) return;
    commit('SET_IS_SUBMITTING', true);
    try {
      const formData = new FormData();
      formData.append("file", file, file.name);
      const data = await uploadFile(formData); // 提交表单
      commit('SET_FORM_STATUS', '上传成功');
      return data;
    } catch (error) {
      console.error('上传文件失败:', formatError(error));
      commit('SET_FORM_STATUS', '上传失败');
      throw error; // 抛出错误，以便调用者可以捕获并处理
    } finally {
      commit('SET_IS_SUBMITTING', false);
    }
  },

  setTempToken({ commit }, tempToken) {
    commit('SET_TEMP_TOKEN', tempToken);
    localStorage.setItem('tempToken', tempToken);
  },
  removeAllToken({ commit }) {
    commit('SET_TEMP_TOKEN', '');
    commit('SET_TOKEN', '');
    localStorage.removeItem('tempToken');
    localStorage.removeItem('token');
  },
  setToken({ commit }, token) {
    commit('SET_TOKEN', token);
    localStorage.setItem('token', token);
  },

  // 获取考勤规则
  async fetchAttendanceRules({ commit, state }) {
    try {
      const data = await getAttendanceRule();
      commit('SET_ATTENDANCE_RULES', data);
      commit('SET_ATTENDANCE_RULES_LOADED', true);
      return data;
    } catch (error) {
      console.error('获取考勤规则失败:', formatError(error));
      commit('SET_ATTENDANCE_RULES_LOADED', false);
      throw error;
    }
  },

  // 获取当前打卡信息
  async fetchCurrentSignInfo({ commit, state }) {
    try {
      const data = await getSignCurrentSign();
      commit('SET_CURRENT_SIGN_INFO', data);
      return data;
    } catch (error) {
      console.error('获取当前打卡信息失败:', formatError(error));
      commit('SET_CURRENT_SIGN_INFO_LOADED', false);
      throw error;
    }
  },
};
