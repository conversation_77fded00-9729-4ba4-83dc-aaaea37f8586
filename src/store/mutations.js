// 同步改变state

const mainMutation = {
  SET_HRID: (state, hrId) => {
    state.hrId = hrId;
    localStorage.setItem("hrId", hrId)
  },
  // device id
  SET_DEVICEID: (state, deviceId) => {
    state.deviceId = deviceId;
  },
  SET_DEVICE_TYPE: (state, deviceType) => {
    state.deviceType = deviceType;
  },
  SET_ENTERSOURCE: (state, enterSource) => {
    state.enterSource = enterSource;
  },
  SET_FROMROUTENAME: (state, fromRouteName) => {
    state.fromRouteName = fromRouteName;
  },
  SET_FROMROUTEQUERY: (state, fromRouteQuery) => {
    state.fromRouteQuery = fromRouteQuery;
  },
  SET_personInfoObj: (state, personInfoObj) => {
    window.localStorage.setItem(personInfoObj.key, JSON.stringify(personInfoObj.value))
  },
  SET_EMAIL(state, email) {
    state.email = email;
  },
  SET_VERIFICATION_CODE(state, code) {
    state.verificationCode = code;
  },
  SET_VERIFICATION_STATUS(state, status) {
    state.verificationStatus = status;
  },
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo;
  },
  SET_FORM_DATA(state, formData) {
    state.userInfo = {
      ...state.userInfo,
      ...formData
    };
  },
  SET_FORM_STATUS(state, status) {
    state.formStatus = status;
  },
  SET_IS_SUBMITTING(state, isSubmitting) {
    state.isSubmitting = isSubmitting;
  },
  SET_VERIFICATION_SENT(state, status) {
    state.verificationSent = status;
  },
  SET_TEMP_TOKEN(state, token) {
    state.tempToken = token;
  },
  SET_TOKEN(state, token) {
    state.token = token;
  },
  SET_LOGOUT_DIALOG_VISIBLE(state, status) {
    state.logoutDialogVisible = status;
  },
  // 考勤规则相关 mutations
  SET_ATTENDANCE_RULES(state, rules) {
    state.attendanceRules = rules;
  },
  SET_ATTENDANCE_RULES_LOADED(state, loaded) {
    state.attendanceRulesLoaded = loaded;
  },
  // 当前打卡信息相关 mutations
  SET_CURRENT_SIGN_INFO(state, signInfo) {
    state.currentSignInfo = signInfo;
    state.currentSignInfoLoaded = true;
  },
  SET_CURRENT_SIGN_INFO_LOADED(state, loaded) {
    state.currentSignInfoLoaded = loaded;
  },
};
export default mainMutation;
