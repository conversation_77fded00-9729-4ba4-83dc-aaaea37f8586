import { getQueryString } from "@/utils/tools";

console.log('初始化化顺序1-state.js')

let productSource = ''
const params = getQueryString()
if (params.product) {
  params.product = params.product.toLowerCase()
}
productSource = params.product


if (!productSource) {
  productSource = localStorage.getItem('productSource')
}

const mainState = {
  tempToken: '',
  token: '',
  email: '',
  verificationCode: '', // 存储验证码
  verificationSent: false, // 是否已发送验证码
  verificationStatus: null, // 验证码验证状态
  userInfo: null, // 用户信息
  formData: {
    name: '',
    phone: '',
    address: ''
  },
  formStatus: null, // 表单提交状态
  isSubmitting: false, // 是否正在提交表单
  logoutDialogVisible: false,
  unit: '', // 当前产品货币单位
  productSource: productSource,
  contract: '',
  hrId: localStorage.getItem('hrId') || '',
  deviceId: '',
  deviceType: '',
  enterSource: '',
  upAmount: "",
  fromRouteName: '',
  fromRouteQuery: {}, // 前一个页面带过来的请求参数
  personInfoObj: {}, // 存储本地数据，第一个个人资料页
  // 考勤规则相关状态（只包含规则配置）
  attendanceRules: {
    specialFlag: 0, // 考勤规则类型：0常规 1特殊
    dateType: 1, // 日期时间值类型 1每周几 2每月几号 3时间(HH:mm:ss) 4.日期(yyyy-MM-dd)
    dateTime: "1", // 日期时间值
    workFlag: 1, // 是否工作标识：0否 1是
    attendanceType: null, // 考勤类型 1固定考勤 2弹性考勤
    workStartTime: "08:00:00", // 固定考勤，开始时间
    workStartTimeUTC: "08:00:00", // 固定考勤，开始时间UTC
    workEndTime: "16:00:00", // 固定考勤，结束时间
    workEndTimeUTC: "16:00:00", // 固定考勤，结束时间UTC
    workDuration: null, // 工作时长
    earliestClockIn: null, // 最早打卡时间
    earliestClockInUTC: null, // 最早打卡时间UTC
    latestClockIn: null, // 最晚打卡时间
    latestClockInUTC: null, // 最晚打卡时间UTC
    breakFlag: 1, // 是否工作休息：0否 1是
    breakStartTime: "12:00:00", // 休息开始时间
    breakStartTimeUTC: "12:00:00", // 休息开始时间UTC
    breakEndTime: "13:30:00", // 休息结束时间
    breakEndTimeUTC: "13:30:00", // 休息结束时间UTC
    clockType: 1, // 考勤类型：0不限 1限制
    clockLocationList: [], // 考勤位置列表
    attendanceName: "", // 考勤名称
    attendanceRule: "", // 考勤规则
    now: "" // 当前时间
  },
  attendanceRulesLoaded: false, // 考勤规则是否已加载
  // 当前打卡信息相关状态（来自 /attendance/clock/current 接口）
  currentSignInfo: {
    attendanceDate: null, // 考勤日期：yyyy-MM-dd
    clockInTime: null, // 上班打卡时间：yyyy-MM-dd hh:mm:ss
    clockOutTime: null, // 下班打卡时间：yyyy-MM-dd hh:mm:ss
    workFlag: 1, // 是否工作标识：0否 1是
    status: null, // 状态 0正常 非0异常
    onStatus: null, // 上班卡状态 0正常 1迟到 3忘记打卡
    onApprovalId: null, // 上班卡审核ID
    onApprovalStatus: null, // 上班卡审核状态 0审核中 1成功 2拒绝
    offStatus: null, // 下班卡状态 0正常 2早退 3忘记打卡
    offApprovalId: null, // 下班卡审核ID
    offApprovalStatus: null, // 下班卡审核状态 0审核中 1成功 2拒绝
    lateArrivalNum: 0, // 迟到数量
    leavingEarlyNum: 0, // 早退数量
    notClockingInNum: 0 // 未打卡数量
  },
  currentSignInfoLoaded: false, // 当前打卡信息是否已加载
}

export default mainState
